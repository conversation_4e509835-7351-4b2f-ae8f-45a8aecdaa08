<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scratch - B | Vthon Academy</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Shooting stars animation */
        .shooting-star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #FFD700;
            border-radius: 50%;
            box-shadow: 0 0 10px #FFD700;
            animation: shoot 3s linear infinite;
        }

        @keyframes shoot {
            0% {
                transform: translateX(-100px) translateY(100px);
                opacity: 1;
            }
            100% {
                transform: translateX(100vw) translateY(-100vh);
                opacity: 0;
            }
        }

        /* Floating particles */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 215, 0, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .class-header {
            background: transparent;
            color: white;
            padding: 60px 0;
            text-align: center;
            margin-top: 80px;
            position: relative;
            z-index: 1;
        }

        .class-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            word-wrap: break-word;
        }

        .class-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            word-wrap: break-word;
        }

        .navbar {
            background: rgba(26, 26, 46, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            border-bottom: 1px solid rgba(255, 215, 0, 0.2);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: #FFD700;
            text-decoration: none;
        }

        .logo i {
            margin-right: 0.5rem;
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 8px;
        }

        .nav-links a:hover {
            color: #FFD700;
            background: rgba(255, 215, 0, 0.1);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .class-info {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem auto;
            max-width: 800px;
            text-align: center;
        }

        .class-info h2 {
            color: #FFD700;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .class-info p {
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .class-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .class-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .class-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
        }

        .class-card i {
            font-size: 3rem;
            color: #FFD700;
            margin-bottom: 1rem;
        }

        .class-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #FFD700;
        }

        .class-card p {
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .btn {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: #1a1a2e;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .lessons-section {
            margin: 3rem 0;
        }

        .lessons-section h2 {
            text-align: center;
            color: #FFD700;
            margin-bottom: 2rem;
            font-size: 2rem;
        }

        .lesson-item {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 15px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .lesson-header {
            padding: 1.5rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background 0.3s ease;
        }

        .lesson-header:hover {
            background: rgba(255, 215, 0, 0.1);
        }

        .lesson-title {
            font-weight: 600;
            color: #FFD700;
        }

        .lesson-content {
            padding: 0 1.5rem 1.5rem;
            display: none;
        }

        .lesson-content.active {
            display: block;
        }

        .lesson-content p {
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .lesson-content ul {
            margin-left: 1.5rem;
            margin-bottom: 1rem;
        }

        .lesson-content li {
            margin-bottom: 0.5rem;
        }

        .access-denied {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #ff6b6b;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        .access-granted {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4caf50;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        .students-display {
            margin-top: 20px;
        }

        .student-avatars {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
            justify-content: center;
        }

        .student-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
            position: relative;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .student-avatar:hover {
            transform: scale(1.1);
        }

        .student-avatar:hover::after {
            content: attr(data-name);
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
        }

        .student-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        @media (max-width: 768px) {
            .nav-container {
                padding: 0 1rem;
            }
            
            .nav-links {
                gap: 1rem;
            }
            
            .class-header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 0 1rem;
            }
            
            .class-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Shooting stars -->
    <div class="shooting-star" style="top: 20%; animation-delay: 0s;"></div>
    <div class="shooting-star" style="top: 40%; animation-delay: 2s;"></div>
    <div class="shooting-star" style="top: 60%; animation-delay: 4s;"></div>
    <div class="shooting-star" style="top: 80%; animation-delay: 6s;"></div>

    <!-- Floating particles -->
    <div class="particle" style="top: 10%; left: 10%; animation-delay: 0s;"></div>
    <div class="particle" style="top: 20%; left: 80%; animation-delay: 1s;"></div>
    <div class="particle" style="top: 70%; left: 20%; animation-delay: 2s;"></div>
    <div class="particle" style="top: 80%; left: 70%; animation-delay: 3s;"></div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="../index.html" class="logo">
                <i class="fas fa-graduation-cap"></i>
                VTA
            </a>
            <ul class="nav-links">
                <li><a href="../index.html">Trang Chủ</a></li>
                <li><a href="../auth/index.html">Log In</a></li>
                <li><a href="../ranking.html">Bảng Xếp Hạng</a></li>
                <li><a href="../achievements.html">Thành Tích</a></li>
                <li><a href="../index.html#courses">Khóa Học</a></li>
                <li><a href="../index.html#contact">Liên Hệ</a></li>
            </ul>
        </div>
    </nav>

    <!-- Class Header -->
    <section class="class-header">
        <div class="container">
            <h1><i class="fas fa-puzzle-piece"></i> Scratch - B</h1>
            <p>Lớp học Scratch nâng cao - T7 CN 21h00 đến 22h00</p>
        </div>
    </section>

    <!-- Access Control Message -->
    <section class="class-info">
        <div class="container">
            <div id="accessMessage" style="display: none;"></div>

            <div id="classContent" style="display: none;">
                <!-- Class Information -->
                <div class="class-grid">
                    <div class="class-card">
                        <i class="fas fa-calendar-alt"></i>
                        <h3>Lịch Học</h3>
                        <p>Thứ 7 - Chủ Nhật<br>21:00 - 22:00</p>
                    </div>
                    <div class="class-card">
                        <i class="fas fa-users"></i>
                        <h3>Học Viên</h3>
                        <p id="studentCount">Đang tải...</p>
                        <div class="students-display">
                            <div id="studentAvatars" class="student-avatars"></div>
                        </div>
                    </div>
                    <div class="class-card">
                        <i class="fas fa-video"></i>
                        <h3>Google Meet</h3>
                        <a href="#" target="_blank" class="btn">
                            <i class="fas fa-video"></i> Tham gia lớp học
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Coming Soon Section -->
    <section class="lessons-section" id="lessonsSection" style="display: none;">
        <div class="container">
            <div class="class-info">
                <i class="fas fa-rocket"></i>
                <h2>Lớp học sắp ra mắt!</h2>
                <p>Nội dung bài học cho lớp Scratch - B đang được chuẩn bị kỹ lưỡng.</p>
                <p>Vui lòng theo dõi thông báo từ giáo viên để biết thêm chi tiết về thời gian bắt đầu.</p>
            </div>
        </div>
    </section>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, collection, query, where, getDocs } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Class ID for this class
        const CLASS_ID = 'scratch-b';

        // Check if user has access to this class
        async function checkAccess(user) {
            if (!user) {
                return false;
            }

            try {
                const userDoc = await getDoc(doc(db, "users", user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();

                    // Check if user is admin
                    const isAdmin = userData.isAdmin || user.email === '<EMAIL>';

                    // Admin has access to all classes, or user has access to their selected class
                    return isAdmin || userData.courseClass === CLASS_ID;
                }
                return false;
            } catch (error) {
                console.error("Error checking access:", error);
                return false;
            }
        }

        // Load students for this class
        async function loadStudents() {
            try {
                const q = query(
                    collection(db, "users"),
                    where("courseClass", "==", CLASS_ID)
                );
                const querySnapshot = await getDocs(q);

                const students = [];
                querySnapshot.forEach((doc) => {
                    const userData = doc.data();
                    if (userData.fullName && userData.fullName.trim() !== '') {
                        students.push({
                            id: doc.id,
                            name: userData.fullName,
                            avatar: userData.avatar || '../assets/images/avatars/avatar_boy_1.png'
                        });
                    }
                });

                // Update student count
                document.getElementById('studentCount').textContent = `${students.length}/8 học viên`;

                // Display student avatars
                const avatarsContainer = document.getElementById('studentAvatars');
                if (students.length > 0) {
                    avatarsContainer.innerHTML = students.map(student => `
                        <div class="student-avatar" data-name="${student.name}" onclick="viewStudentProfile('${student.id}')">
                            <img src="${student.avatar}" alt="${student.name}">
                        </div>
                    `).join('');
                } else {
                    avatarsContainer.innerHTML = '<p style="color: #666; font-style: italic;">Chưa có học viên nào</p>';
                }
            } catch (error) {
                console.error("Error loading students:", error);
                document.getElementById('studentCount').textContent = 'Lỗi tải dữ liệu';
            }
        }

        // View student profile
        window.viewStudentProfile = function(userId) {
            window.location.href = `../auth/student-profile.html?userId=${userId}`;
        };

        // Auth state change listener
        onAuthStateChanged(auth, async (user) => {
            const accessMessage = document.getElementById('accessMessage');
            const classContent = document.getElementById('classContent');
            const lessonsSection = document.getElementById('lessonsSection');

            if (user) {
                const hasAccess = await checkAccess(user);

                if (hasAccess) {
                    accessMessage.style.display = 'none';
                    classContent.style.display = 'block';
                    lessonsSection.style.display = 'block';

                    // Load students
                    await loadStudents();
                } else {
                    accessMessage.innerHTML = `
                        <div class="access-denied">
                            <h3><i class="fas fa-lock"></i> Không có quyền truy cập</h3>
                            <p>Bạn hiện không có quyền truy cập lớp học này!</p>
                            <p>Vui lòng liên hệ giáo viên để được phân lớp hoặc kiểm tra lại thông tin lớp học đã chọn trong tài khoản.</p>
                        </div>
                    `;
                    accessMessage.style.display = 'block';
                    classContent.style.display = 'none';
                    lessonsSection.style.display = 'none';
                }
            } else {
                accessMessage.innerHTML = `
                    <div class="access-denied">
                        <h3><i class="fas fa-sign-in-alt"></i> Vui lòng đăng nhập</h3>
                        <p>Bạn cần đăng nhập để truy cập lớp học này.</p>
                        <a href="../auth/" class="btn">
                            <i class="fas fa-sign-in-alt"></i> Đăng nhập ngay
                        </a>
                    </div>
                `;
                accessMessage.style.display = 'block';
                classContent.style.display = 'none';
                lessonsSection.style.display = 'none';
            }
        });
    </script>

    <script>
        // Lesson toggle functionality
        document.querySelectorAll('.lesson-header').forEach(header => {
            header.addEventListener('click', () => {
                const content = header.nextElementSibling;
                const icon = header.querySelector('i');

                content.classList.toggle('active');
                icon.classList.toggle('fa-chevron-down');
                icon.classList.toggle('fa-chevron-up');
            });
        });

        // Create more shooting stars dynamically
        function createShootingStar() {
            const star = document.createElement('div');
            star.className = 'shooting-star';
            star.style.top = Math.random() * 100 + '%';
            star.style.animationDelay = Math.random() * 3 + 's';
            document.body.appendChild(star);

            setTimeout(() => {
                star.remove();
            }, 3000);
        }

        // Create shooting stars periodically
        setInterval(createShootingStar, 2000);

        // Create floating particles
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 6 + 's';
            document.body.appendChild(particle);

            setTimeout(() => {
                particle.remove();
            }, 6000);
        }

        // Create particles periodically
        setInterval(createParticle, 1000);
    </script>
</body>
</html>
